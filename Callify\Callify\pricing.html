<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Callify - Use Cases</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
    <link rel="stylesheet" href="./style.css" />
</head>

<body>
    <div class="glow-ball"></div>
    <div class="glow-ball-small"></div>

    <!-- Hero Section -->
    <section class="pricing__hero">
        <div class="pricing__hero__content">
            <h1 class="fw-bold">
                Callify
                <span class="toInspire">Pricing Page</span>
            </h1>
            <p class="mt-3 heroText">
                Choose The Perfect Plan For Your Talent Acquisition And
                Management Needs
            </p>
        </div>
    </section>

    <!-- Pricing Cards -->
    <section class="pricing__cards">
        <div class="pricing__cards__container">
            <!-- Callify GREEN -->
            <article class="pricing__card card--green" aria-labelledby="green-title">
                <header class="pricing__card__header">
                    <h3 id="green-title">Callify GREEN</h3>
                    <p class="sub">Sourcing to Screening</p>
                </header>

                <ul class="pricing__card__features" aria-hidden="false">
                    <li><span class="tick" aria-hidden="true"><svg viewBox="0 0 24 24" width="14" height="14"
                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>QR Code Sourcing</li>
                    <li><span class="tick" aria-hidden="true"><svg viewBox="0 0 24 24" width="14" height="14"
                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Meta Ads Sourcing</li>
                    <li><span class="tick" aria-hidden="true"><svg viewBox="0 0 24 24" width="14" height="14"
                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Knockout Screening (via Calls)</li>
                    <li><span class="tick" aria-hidden="true"><svg viewBox="0 0 24 24" width="14" height="14"
                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Knockout Screening (via BOT)</li>
                </ul>

                <footer class="pricing__card__footer">
                    <div class="pricing__card__price">Per Credit basis</div>
                    <a class="pricing__card__btn" href="#" role="button">Product Details</a>
                </footer>
            </article>

            <!-- Callify BLUE -->
            <article class="pricing__card card--blue" aria-labelledby="blue-title">
                <header class="pricing__card__header">
                    <h3 id="blue-title">Callify BLUE</h3>
                    <p class="sub">Screening to Joining</p>
                </header>

                <ul class="pricing__card__features">
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Pre-Screening</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Assessment Interviews</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Interview Scheduling</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Post-offer Follow-up</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Pre-Onboarding</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Notice Period Engagement</li>
                </ul>

                <footer class="pricing__card__footer">
                    <div class="pricing__card__price">Per Credit basis</div>
                    <a class="pricing__card__btn" href="#" role="button">Product Details</a>
                </footer>
            </article>

            <!-- Callify PINK (Most Popular) -->
            <article class="pricing__card card--pink most-popular" aria-labelledby="pink-title">
                <div class="popular-badge">Most Popular</div>

                <header class="pricing__card__header">
                    <h3 id="pink-title">Callify PINK</h3>
                    <p class="sub">E2E TA &amp; TM Processes</p>
                </header>

                <ul class="pricing__card__features">
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Talent Acquisition</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Pre-screening, Assessment, Scheduling</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Post Offer Follow-up, Pre-Onboarding</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Notice Period engagement, Joining</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Talent Management</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Day-1 Orientation; Policy Queries Finance & Payroll Queries, Leave Management</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Travel Entitlement Queries</li>
                </ul>

                <footer class="pricing__card__footer">
                    <div class="pricing__card__price">Per Minute basis</div>
                    <a class="pricing__card__btn" href="#" role="button">Product Details</a>
                </footer>
            </article>

            <!-- Callify ENTERPRISE -->
            <article class="pricing__card card--enterprise" aria-labelledby="enterprise-title">
                <header class="pricing__card__header">
                    <h3 id="enterprise-title">Callify ENTERPRISE</h3>
                    <p class="sub">All Products</p>
                </header>

                <ul class="pricing__card__features">
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Access to all products (Callify GREEN, BLUE and PINK)</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>Charged as per product usage</li>
                    <li><span class="tick"><svg viewBox="0 0 24 24" width="14" height="14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg></span>As per use-case within respective products</li>
                </ul>

                <footer class="pricing__card__footer">
                    <div class="pricing__card__price">As per use-case within the respective products</div>
                    <a class="pricing__card__btn" href="#" role="button">Custom Pricing</a>
                </footer>
            </article>

        </div>
    </section>

    <!-- Contact Sales -->
    <section class="contact__sales">
        <div class="contact__sales__content text-center py-5">
            <h2 class="contact__sales__heading">Contact Sales for details & pricing</h2>
            <p class="contact__sales__text">
                Get in touch with our sales team to discuss custom solutions and enterprise pricing
            </p>
            <button class="contact__sales__btn">Contact Sales</button>
        </div>
    </section>

</body>

</html>