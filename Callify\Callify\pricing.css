/* ========================================== Base Styles ========================================== */
:root {
  --theme-color-primary: #de1b76;
  --theme-color-black: #080808;
  --theme-color-secondory: #8e51ff;
  --theme-color-grey: #90a1b9;
}

body {
  margin: 0;
  background-color: var(--theme-color-black);
  background-image: radial-gradient(var(--theme-color-grey) -11.5px,
      transparent 3.5px);
  background-size: 25px 25px;
  color: white;
  background-attachment: fixed;
  font-family: "Inter", sans-serif;
  font-style: normal;
  overflow-x: hidden;
  padding-inline: 60px;
}

/* Glow Ball */
.glow-ball,
.glow-ball-small {
  position: absolute;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%,
      var(--theme-color-primary),
      var(--theme-color-secondory));
  filter: blur(80px);
  opacity: 0.6;
  animation: float 12s ease-in-out infinite alternate;
}

.glow-ball-small {
  width: 150px;
  height: 150px;
  animation: spin 12s ease-in-out infinite alternate;
  filter: blur(60px);
}

@media (max-width: 768px) {
  .glow-ball,
  .glow-ball-small {
    display: none;
  }
}

@keyframes float {
  0% {
    transform: translate(-50%, -50%) scale(1);
    top: 10%;
    left: 20%;
  }
  25% {
    top: 70%;
    left: 30%;
    transform: translate(-50%, -50%) scale(1.1);
  }
  50% {
    top: 50%;
    left: 80%;
    transform: translate(-50%, -50%) scale(0.9);
  }
  75% {
    top: 30%;
    left: 60%;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    top: 10%;
    left: 20%;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) scale(1);
    bottom: 10%;
    left: 50%;
  }
  25% {
    bottom: 70%;
    left: 30%;
    transform: translate(-50%, -50%) scale(1.1);
  }
  50% {
    bottom: 50%;
    left: 20%;
    transform: translate(-50%, -50%) scale(0.9);
  }
  75% {
    bottom: 30%;
    left: 70%;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    bottom: 10%;
    left: 60%;
    transform: translate(-50%, -50%) scale(1);
  }
}

.toInspire {
  background: linear-gradient(270.53deg, #f6339a 8.68%, #8e51ff 101.38%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  white-space: nowrap;
}

.heroText {
  font-size: 18px;
  font-weight: 300;
  line-height: 26px;
}

.fw-bold {
  font-weight: 700 !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

/* Responsive text visibility */
.desktop-text {
  display: block;
}

.mobile-text {
  display: none;
}

@media (max-width: 768px) {
  .desktop-text {
    display: none;
  }

  .mobile-text {
    display: block;
  }
}

/* ========================================== Pricing ========================================== */
/* Pricing Hero Section */
.pricing__hero {
  max-width: 1340px;
  position: relative;
  text-align: center;
  color: white;
  padding: 40px 60px;
  margin-inline: auto;
  margin-top: 4rem;
  min-height: 200px;
  height: auto;
  overflow: visible;
  border-radius: 20px;
  background: linear-gradient(135deg,
      rgba(15, 23, 43, 0.3) 0%,
      rgba(29, 41, 61, 0.3) 100%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.pricing__hero__content {
  position: relative;
  z-index: 2;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.pricing__hero__content h1 {
  font-size: clamp(1.5rem, 5vw, 2.5rem);
}

.pricing__hero__content .heroText {
  font-size: clamp(0.9rem, 3vw, 1.2rem);
  margin-top: 1rem;
  line-height: 1.5;
  width: 120%;
}

.mobile-text {
  display: none;
}



/* Mobile fix */
@media (max-width: 768px) {
  .pricing__hero {
    padding: 30px 15px;
    margin-top: 2rem;
  }

  .pricing__hero__content h1 {
    font-size: 1.8rem;
    text-align: center;
  }

  .pricing__hero__content .heroText {
    font-size: 1rem;
    text-align: center;
  }

  .desktop-text {
    display: none;
  }

  .mobile-text {
    display: block;
  }
}

/*! Pricing Cards */
.pricing__cards {
  --card-radius: 16px;
  --gap: 25px;
  padding: 64px 0px;
  position: relative;
  overflow: hidden;
  color: #fff;
  max-width: 1340px;
  margin: 0 auto;
}

/* dotted backdrop */
.pricing__cards::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 26px 26px;
  opacity: 0.28;
  pointer-events: none;
  z-index: 0;
}

.pricing__cards__container {
  max-width: 1340px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--gap);
  align-items: stretch;
}

/* General card */
.pricing__card {
  padding: 35px 22px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.04);
  transition: transform .28s ease, box-shadow .28s ease;
  max-width: 600px;
  min-height: 640px;
  border-radius: 14px;
  border-width: 1px;
  box-shadow: 0px 1px 2px -1px #0000001A;
  box-shadow: 0px 1px 3px 0px #0000001A;
  backdrop-filter: blur(24px)
}

/* Card header */
.pricing__card__header h3 {
  font-size: 20px;
  margin: 0 0 8px;
  font-family: Inter;
  font-weight: 700;
  text-align: center;
  line-height: 28px;
  letter-spacing: 0%;

}

.pricing__card__header .sub {
  margin: 0;
  color: #D1D5DC;
  text-align: center;
  font-family: Inter;
  font-weight: 300;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;

}

/* Features */
.pricing__card__features {
  margin: 18px 0;
  padding: 0;
  list-style: none;
  display: block;
  gap: 10px;
  flex: 1 1 auto;
}

.pricing__card__features li {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin: 12px 0;
  color: #E5E7EB;
  font-family: Inter;
  font-weight: 300;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;

}

/* inline tick SVG inherits color from .tick color */
.pricing__card__features .tick {
  display: inline-flex;
  min-width: 20px;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
  color: #F6339A;
}

/* Footer */
.pricing__card__footer {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 12px;
}

.pricing__card__price {
  color: #FFFFFF;
  font-family: Inter;
  font-weight: 500;
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0%;
  text-align: center;

}

.pricing__card__btn {
  display: inline-block;
  width: 100%;
  text-align: center;
  padding: 10px 16px;
  border-radius: 999px;
  text-decoration: none;
  font-weight: 400;
  background: #DE1B76;
  color: #fff;
  font-size: 14px;
  box-shadow: 0 6px 18px rgba(246, 51, 154, 0.12);
  border: none;
}

/* ---------------- Individual card styles ---------------- */
.card--green {
  background: linear-gradient(135deg, rgba(6, 181, 22, 0.2) 0%, rgba(3, 138, 16, 0.2) 100%);
  color: #E8F8EE;
  border: 1px solid #06B5164D;
}

.card--blue {
  background: linear-gradient(135deg, rgba(43, 127, 255, 0.2) 0%, rgba(0, 146, 184, 0.2) 100%);
  border: 1px solid #2B7FFF4D;
  color: #DFF1FF;
}

.card--pink {
  background: linear-gradient(135deg, rgba(246, 51, 154, 0.2) 0%, rgba(236, 0, 63, 0.2) 100%);
  border: 1px solid #F6339A4D;
  color: #FFDFEF;
  box-shadow: 0px 1px 2px -1px #0000001A;
  box-shadow: 0px 1px 3px 0px #0000001A;
  box-shadow: 0px 0px 0px 2px #F6339A80;
  backdrop-filter: blur(24px)
}

.card--enterprise {
  background: #30303033;
  border: 1px solid #3030304D;
  color: #E6E6E6;
  /* border-width: 1px; */
  border: 1px solid #c8c8c84d;

}

.card--enterprise .pricing__card__features .tick {
  color: #F6339A;
}

/* Most Popular badge */
.most-popular .popular-badge {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -18px;
  z-index: 2;
  background: linear-gradient(360deg, #72324a, #4f132f);
  color: #fff;
  padding: 8px 18px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 700;
  width: 207px;
  text-align: center;
  height: 37px;
  border-radius: 16777200px;
  font-family: Geist;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
}

/* small visual top-notch for the popular card */
.most-popular::after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.02), 0 0 60px rgba(246, 51, 154, 0.035);
  pointer-events: none;
}

/* ---------- Responsive ---------- */
@media (max-width: 1100px) {
  .pricing__cards__container {
    grid-template-columns: repeat(2, 1fr);
  }

  .pricing__card {
    min-height: 520px;
  }
}

@media (max-width: 640px) {
  .pricing__cards {
    padding: 20px;
    /* margin-top: 2rem; */
  }

  .card--pink{
    box-shadow: none;
  }

  .pricing__cards__container {
    /* grid-template-columns: 1fr; */
    display: flex;
    flex-direction: column;
    min-width: 261px;
    padding-top: 44px;
    padding-bottom: 24px;
    gap: 30px;
  }

  .pricing__card {
    padding: 25px 20px;
  }

  .pricing__card__features li {
    font-size: 14px;
  }

  .pricing__card.card--pink {
    order: 1;
  }

  .pricing__card.card--blue {
    order: 2;
  }

  .pricing__card.card--green {
    order: 3;
  }

  .pricing__card.card--enterprise {
    order: 4;
  }

  .most-popular .popular-badge {
    top: -20px;
    padding: 6px 14px;
    font-size: 12px;
  }

  .pricing__card__btn {
    padding: 12px;
  }
}

/*! Contact Sales */
.contact__sales {
  padding: 30px 60px;
  max-width: 1340px;
  margin: 0 auto;
}

.contact__sales__content {
  background: linear-gradient(90deg, rgba(16, 24, 40) 0%, rgba(0, 0, 0) 100%);
  height: 100%;
  width: 514px;
  height: 226px;
  border-radius: 14px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: auto;
  padding: 2rem 1.5rem;
  box-shadow: 0px 1px 2px -1px #0000001A;
  box-shadow: 0px 1px 3px 0px #0000001A;
  backdrop-filter: blur(24px)
}

.contact__sales__heading {
  font-weight: 700;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: 0%;
  text-align: center;
}

.contact__sales__text {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  text-align: center;
  color: #D1D5DC;
  width: 377px;
  height: 48px;
}

.contact__sales__btn {
  width: 157px;
  height: 40px;
  opacity: 1;
  border-radius: 8px;
  background: #F6339A;
  color: #fff;
  border: none;
  box-shadow: 0px 1px 2px 0px #0000000D;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  padding: 10px 20px;
  margin-top: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .contact__sales__content {
    width: 100%;
    max-width: 100%;
    height: auto;
    padding: 1.5rem 1rem;
  }

  .contact__sales__heading {
    font-size: 20px;
    line-height: 28px;
  }

  .contact__sales__text {
    width: 100%;
    max-width: 100%;
    font-size: 14px;
    line-height: 20px;
    height: auto;
    margin-top: 0.5rem;
  }

  .contact__sales__btn {
    width: 100%;
    max-width: 200px;
    height: auto;
    padding: 10px;
    font-size: 14px;
    margin-top: 1rem;
  }
}