:root {
  --theme-color-primary: #de1b76;
  --theme-color-black: #080808;
  --theme-color-secondory: #8e51ff;
  --theme-color-grey: #90a1b9;
}

body {
  margin: 0;
  background-color: var(--theme-color-black);
  background-image: radial-gradient(var(--theme-color-grey) -11.5px,
      transparent 3.5px);
  background-size: 25px 25px;
  color: white;
  background-attachment: fixed;
  font-family: "Inter", sans-serif;
  font-style: normal;
  overflow-x: hidden;
}

ul {
  padding-left: 1.2rem;
}

/* Glow Ball */
.glow-ball,
.glow-ball-small {
  position: absolute;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%,
      var(--theme-color-primary),
      var(--theme-color-secondory));
  filter: blur(80px);
  opacity: 0.6;
  animation: float 12s ease-in-out infinite alternate;
}

.glow-ball-small {
  width: 150px;
  height: 150px;
  animation: spin 12s ease-in-out infinite alternate;
  filter: blur(60px);
}

@media (max-width: 768px) {

  .glow-ball,
  .glow-ball-small {
    display: none;
  }
}

@keyframes float {
  0% {
    transform: translate(-50%, -50%) scale(1);
    top: 10%;
    left: 20%;
  }

  25% {
    top: 70%;
    left: 30%;
    transform: translate(-50%, -50%) scale(1.1);
  }

  50% {
    top: 50%;
    left: 80%;
    transform: translate(-50%, -50%) scale(0.9);
  }

  75% {
    top: 30%;
    left: 60%;
    transform: translate(-50%, -50%) scale(1.2);
  }

  100% {
    top: 10%;
    left: 20%;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) scale(1);
    bottom: 10%;
    left: 50%;
  }

  25% {
    bottom: 70%;
    left: 30%;
    transform: translate(-50%, -50%) scale(1.1);
  }

  50% {
    bottom: 50%;
    left: 20%;
    transform: translate(-50%, -50%) scale(0.9);
  }

  75% {
    bottom: 30%;
    left: 70%;
    transform: translate(-50%, -50%) scale(1.2);
  }

  100% {
    bottom: 10%;
    left: 60%;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Hero Section */
.hero {
  max-width: 1200px;
  position: relative;
  text-align: center;
  color: white;
  padding: 50px 10px 20px 10px;
  margin-inline: auto;
  width: 90%;
  margin-top: 4rem;
  min-height: 85vh;
  overflow: hidden;
  border-radius: 20px;
  background: linear-gradient(135deg,
      rgba(15, 23, 43, 0.3) 0%,
      rgba(29, 41, 61, 0.3) 100%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.hero-content {
  position: relative;
  z-index: 2;
  padding: 1rem 1rem 1rem 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 70vh;
}

@media (max-width: 768px) {

  .hero {
    padding: 10px 15px;
    min-height: auto;
  }

  .hero-content {
    height: auto;
  }

  .talentAcquisition {
    margin-top: 0 !important;
  }
}

.hrJourneyBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  font-weight: 500;
  background: linear-gradient(90deg,
      rgba(246, 51, 154, 0.2) 0%,
      rgba(142, 81, 255, 0.2) 100%);
  border: 1px solid rgba(251, 100, 182, 0.3);
  /* your 30% opacity */
  border-radius: 30px;
  box-shadow: 0px 0px 25px rgba(236, 72, 153, 0.3);
  /* 30% opacity */
  color: #fda5d5;
  transition: all 0.3s ease;
}

.hrJourneyBtn:hover {
  transform: scale(1.05);
  box-shadow: 0px 0px 35px rgba(236, 72, 153, 0.5);
  background: linear-gradient(90deg,
      rgba(246, 51, 154, 0.2) 0%,
      rgba(142, 81, 255, 0.2) 100%);
}

.hrJourneyBtn img {
  height: 20px;
  width: 20px;
}

.toInspire {
  background: linear-gradient(270.53deg, #f6339a 8.68%, #8e51ff 101.38%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* for Safari */
  color: transparent;
  white-space: nowrap;
}

.heroText {
  font-size: 18px;
  font-weight: 300;
  line-height: 26px;
}

.btn-start {
  background: var(--theme-color-primary);
  border: none;
  padding: 8px 35px 8px 35px;
  border-radius: 50px;
  color: #fff;
  margin-top: 20px;
}

/* Talent */

.talentAcquisition {
  position: relative;
  margin-top: 4rem;
}

.text-white-50 {
  color: #c1c1c1;
  line-height: 20px;
}

/* Toggle container */
.toggle-container {
  display: flex;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
    linear-gradient(90deg,
      rgba(246, 51, 154, 0.2) 0%,
      rgba(142, 81, 255, 0.2) 100%);
  width: 40%;
  margin: auto;
  /* border: 1px solid rgba(251, 100, 182, 0.3); */
  border-radius: 50px;
  padding: 10px 9px;
  overflow: hidden;
  margin-top: 3rem;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1),
    inset 0 0 0px 0px rgba(255, 255, 255, 0);
}

/* Buttons inside toggle */
.toggle-btn {
  flex: 1;
  border: none;
  background: transparent;
  color: #fda5d5;
  padding: 10px 20px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
}

/* Active state */
.toggle-btn.active {
  color: #fff;
  font-weight: 600;
  background: #de1b76;
}

/* Complete Transformation */
.completeTransformation {
  padding: 60px 20px;
  max-width: 1340px;
  margin: 0 auto;
}

.transformation-card {
  max-width: 1300px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(15, 23, 43, 0.3) 0%, rgba(29, 41, 61, 0.3) 100%);
  border-radius: 20px;
  padding: 0;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.transformation-header {
  padding: 30px 25px;
  text-align: center;
}

.transformation__heading {
  font-size: 32px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 15px;
}

.transformtion__text {
  font-size: 16px;
  font-weight: 500;
  color: #C1C1C1;
  line-height: 1.4;
  margin: 0;
}

.transformation__stats {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.stat-item {
  flex-direction: column;
  gap: 10px;
  flex: 1;
  position: relative;
  padding: 0;
  text-align: center;
  display: flex;
  align-items: center;
  padding: 55px 20px;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 40px;
  font-weight: 500;
  color: var(--theme-color-primary);
  min-width: auto;
  text-align: center;
}

.stat-label {
  font-size: 14px;
  font-weight: 400;
  color: #C1C1C1;
  text-align: center;
  line-height: 1.2;
}

/* Mobile View */
@media (max-width: 768px) {

  .completeTransformation {
    padding: 0px 10px;
    margin-top: 0rem;
  }

  .transformation-card {
    max-width: 1300px;
    margin: 0 10px;
    border-radius: 25px;
  }

  .transformation-header {
    display: block;
    padding: 0px 20px 25px;
  }

  .transformation__stats {
    display: block;
    padding: 0;
  }

  .stat-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
    padding: 20px 10px;
    margin: 0 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    text-align: left;
  }

  .stat-item:last-child {
    border-bottom: none;
  }

  .stat-item::after {
    display: none;
  }

  .stat-number {
    font-size: 32px;
    min-width: 80px;
    text-align: left;
  }

  .stat-label {
    font-size: 16px;
    text-align: left;
    flex: 1;
  }
}










/* Tabs row */
.workflow-tabs {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin: 2rem 0;
}

.workflow-tab {
  background: #6464644D;
  border: 1px solid #8080804D;
  border-radius: 10px;
  padding: 14px 10px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 140px;
  height: 108px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0%;
  text-align: center;
  line-height: 18px;
}

.workflow-tab.active {
  background: linear-gradient(90deg, rgba(246, 51, 154, 0.2) 0%, rgba(255, 32, 86, 0.2) 100%);
  border: 1px solid #F6339A;
  color: #F6339A;
}

.tab-arrow {
  color: var(--theme-color-grey);
  font-size: 18px;
}


/* Section wrapper */
.section-card {
  background: linear-gradient(135deg, rgba(15, 23, 43, 0.3) 0%, rgba(29, 41, 61, 0.3) 100%);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 20px;
  padding: 2.5rem;
  margin: auto;
  max-width: 1300px;
  color: #fff;
  display: flex;
  flex-direction: row;
  gap: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Title row */
.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 1.5rem;
  line-height: 20px;
}

.talent-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border-width: 1px;
  background: linear-gradient(135deg, rgba(246, 51, 154, 0.2) 0%, rgba(255, 32, 86, 0.2) 100%);
  border: 1px solid #F6339A4D;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-left-card {
  width: 50%;
  flex: 1;
}

/* Scenario / Challenge / Solution blocks */
.scenario,
.challenge,
.solution {
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  text-align: left;
  font-weight: 300;
  font-size: 12.3px;
  line-height: 17.5px;
  letter-spacing: 0%;
}

.scenario {
  background: #FB2C361A;
  border: 1px solid #FF64674D;
}

.scenario strong {
  color: #FF6467;
}

.scenario-text {
  color: #fff;
}

.challenge {
  background: #2B7FFF1A;
  border: 1px solid #51A2FF4D;
}

.challenge strong {
  color: #51A2FF;
}

.solution {
  background: #A684FF1A;
  border: 1px solid #A684FF80;
}

.solution strong {
  color: #8E51FF;
}

/* Strong heading inside blocks */
.scenario strong,
.challenge strong,
.solution strong {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-weight: 600;
  font-size: 15px;
}

/* Highlight text colors */
.highlight-pink {
  color: #f6339a;
  font-weight: 600;
  font-size: 17px;
  display: contents;
}

/* Impact grid - Add these styles to make boxes equal height */
.impact-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 5px 0;
  margin-top: 1rem;
  height: 100%;
  margin-bottom: .75rem;
}

.impact-grid .col-md-6 {
  display: flex;
  flex: 1;
  min-width: calc(50% - 0.5rem);
}

.impact-box {
  background: #1D293D80;
  border-radius: 10px;
  padding: 1.5rem;
  font-size: 15px;
  font-weight: 500;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: left;
  transition: 0.3s;
  border: 1px solid #31415880;
}

.impact-section {
  width: 50%;
  flex: 1;
}



/* Mobile Responsive */
@media (max-width: 1000px) {

  .toggle-container {
    width: 95%;
  }

  .toggle-btn {
    font-size: 12px;
    padding: 10px;
  }

  .workflow-tabs {
    flex-direction: column;
    gap: .2rem;
  }

  .workflow-tab {
    width: 100%;
    height: auto;
    padding: 1rem;
    font-size: 14px;
    line-height: 1.5;
    text-align: left;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
  }

  .tab-arrow {
    transform: rotate(90deg);
  }

  .tab-arrow img {
    height: 15px;
  }

  .section-card {
    flex-direction: column;
    padding: 0;
    gap: 0;
    align-items: center;
  }

  .section-title {
    padding: 0;
  }

  .section-left-card {
    width: 100%;
  }

  .impact-section {
    width: 100%;
    padding: 0 0.5rem;
  }
}