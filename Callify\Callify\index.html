<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Callify - Use Cases</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
    rel="stylesheet" />
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />

  <!-- FontAwesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  <link rel="stylesheet" href="./style.css" />
</head>

<body>
  <div class="glow-ball"></div>
  <div class="glow-ball-small"></div>
  <!-- Hero Section -->
  <section class="hero">
    <div class="hero-content">
      <button class="btn btn-dark mb-4 hrJourneyBtn">
        <img src="./assets//images//SVG.png" alt="" sizes="" srcset="" />
        Complete HR Journey
      </button>
      <h1 class="fw-bold">
        From Hire
        <span class="toInspire">To Inspire</span>
      </h1>
      <p class="mt-3 heroText">
        Experience The Complete Transformation Of Your HR Processes Through
        AI-Powered Automation. Follow <br />
        The Chronological Journey From Candidate Discovery To Career
        Development.
      </p>
      <button class="btn-start">Start Journey</button>
    </div>
  </section>

  <!-- Talent acquisition -->
  <section class="talentAcquisition">
    <div class="container text-center py-5">
      <h2 class="fw-bold">From Talent Acquisition To Talent Management</h2>
      <p class="text-white-50">
        AI transforms HR from hiring to growth — streamlining sourcing, smart
        selection, faster <br />
        decisions, and continuous talent development for lasting workforce
        excellence.
      </p>

      <div class="toggle-container">
        <button class="toggle-btn active">Talent Acquisition</button>
        <button class="toggle-btn">Talent Management</button>
      </div>

      <div class="workflow-tabs" id="workflowTabs">
      </div>
      <!-- Section -->
      <div id="sectionContainer"></div>
    </div>
  </section>

  <!-- Complete Transformation -->
  <section class="completeTransformation">
    <div class="text-center py-5">
      <div class="transformation-header">
        <h2 class="transformation__heading">Complete Transformation</h2>
        <p class="transformtion__text">
          End-to-end AI automation delivering measurable results across your entire HR lifecycle
        </p>
      </div>

      <div class="transformation-card">
        <div class="transformation__stats">
          <div class="stat-item">
            <span class="stat-number">07</span>
            <span class="stat-label">Use Cases</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">85%</span>
            <span class="stat-label">Average Efficiency Gain</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">60%</span>
            <span class="stat-label">ROI Improvement</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">24/7</span>
            <span class="stat-label">AI Monitoring</span>
          </div>
        </div>
      </div>
    </div>
  </section>


  <script>
    const sections = [
      {
        id: "pre-screening",
        title: "Pre-Screening",
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none"><path d="M24.4999 24.5L19.4365 19.4366" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M12.8333 22.1667C17.988 22.1667 22.1667 17.988 22.1667 12.8333C22.1667 7.67868 17.988 3.5 12.8333 3.5C7.67868 3.5 3.5 7.67868 3.5 12.8333C3.5 17.988 7.67868 22.1667 12.8333 22.1667Z" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        scenario: "Screening job applicants or searched profiles in large numbers to arrive a broadly matching profiles that can be submitted to hiring managers",
        challenge: [
          "Manual interviews lack consistency and objectivity.",
          "Recruiters spend excessive time scheduling and conducting initial interviews.",
          "Delayed insights slow down the hiring cycle.",
          "Leads to loss of business."
        ],
        solution: [
          "<spam class='fw-bold'>Prompt-based or AI-Generated Interview Scripts:</spam> Configure structured question sets for each role and let AI drive the conversation to with evaluative questions based on the predetermined criteria.",
          "<spam class='fw-bold'>Automated Voice Interviews:</spam> Candidates can be interviewed automatically within 15 mins the campaign rollout.",
          "<spam class='fw-bold'>AI-Driven Analysis:</spam> DeepAssess evaluates personality (OCEAN), culture-fit, English communication (CEFR), and honesty indicators.",
          "<spam class='fw-bold'>Instant Insights:</spam> Recruiters receive structured candidate profiles within minutes for faster shortlisting.",
        ],
        impact: [
          { text: "<span class='highlight-pink'>60% faster</span> interview-to-shortlist cycle." },
          { text: "<span class='highlight-pink'>Consistent evaluations</span> across recruiters and locations." },
          { text: "<span class='highlight-pink'>Reduced manual workload</span>, saving 25+ recruiter hours weekly." },
          { text: "Improved candidate experience with prompt, professional engagement." }
        ]
      },
      {
        id: "assessment",
        title: "Assessment",
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none"><path d="M17.5003 2.61328H7.00033C6.38149 2.61328 5.78799 2.85911 5.35041 3.2967C4.91282 3.73428 4.66699 4.32778 4.66699 4.94661V23.6133C4.66699 24.2321 4.91282 24.8256 5.35041 25.2632C5.78799 25.7008 6.38149 25.9466 7.00033 25.9466H21.0003C21.6192 25.9466 22.2127 25.7008 22.6502 25.2632C23.0878 24.8256 23.3337 24.2321 23.3337 23.6133V8.44661L17.5003 2.61328Z" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M16.333 2.61328V7.28029C16.333 7.89918 16.5789 8.49272 17.0165 8.93033C17.4541 9.36795 18.0476 9.6138 18.6665 9.6138H23.3335" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M11.6665 10.7804H9.33301" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M18.668 15.4473H9.33398" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M18.668 20.1133H9.33398" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        scenario: "Deep dive interviews to assess skills in greater depth along with soft skills to enable inviting for interviews with hiring managers.",
        challenge: ["Manual deep-dive interviews often lack standardization, leading to inconsistent assessments.",
          "Evaluators find it difficult to balance technical depth with soft skill insights during limited interaction time.",
          "Scheduling and coordinating these interviews delay the hiring pipeline.",
          "Data from interviews is often unstructured, making it difficult to compare candidates objectively."
        ],

        solution: [
          "<spam class='fw-bold'>AI-Enhanced DeepAssess Framework:</spam> We customize assessment scripts based on role requirements, integrating behavioral, situational, and technical questions.",
          "<spam class='fw-bold'>Structured Virtual Assessments:</spam> Candidates complete advanced AI-led assessments that simulate human-like conversations while maintaining uniform evaluation standards.",
          "<spam class='fw-bold'>Comprehensive Trait Mapping:</spam> DeepAssess scores candidates on personality (OCEAN), culture-fit indicators, English proficiency (CEFR), honesty metrics, and job-specific competencies.",
          "<spam class='fw-bold'>Actionable Reports:</spam> Recruiters receive detailed, structured profiles highlighting strengths, gaps, and role alignment within minutes."
        ],

        impact: [
          { text: "<span class='highlight-pink'>40% reduction</span> in time taken for advanced assessments." },
          { text: "Enhanced <span class='highlight-pink'> accuracy and fairness </span> in evaluations, improving quality-of-hire." },
          { text: "Streamlined process enabling <span class='highlight-pink'> data-backed decisions </span> for shortlisting." },
          { text: "Significantly improved candidate perception of the organization due to quick and professional assessments." }
        ]
      },
      {
        id: "interview",
        title: "Interview Scheduling",
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none"><path d="M9.33398 2.89343V7.56045" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M18.666 2.89343V7.56045" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M22.169 5.22754H5.83448C4.54572 5.22754 3.50098 6.27229 3.50098 7.56105V23.8956C3.50098 25.1844 4.54572 26.2291 5.83448 26.2291H22.169C23.4578 26.2291 24.5025 25.1844 24.5025 23.8956V7.56105C24.5025 6.27229 23.4578 5.22754 22.169 5.22754Z" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M3.5 12.2274H24.5016" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        scenario: "Scheduling of interviews either for Walk-in drives (single date) or for on-on-one interviews with Hiring Managers (multi-date). These can be for both in-person or virtual interview scheduling.",
        challenge: ["Manual scheduling is time-consuming and error-prone, especially for bulk drives.",
          "High chances of double bookings, no-shows, and miscommunication.",
          "Lack of real-time updates leads to confusion for candidates and recruiters.",
          "Limited visibility into available slots for hiring managers and candidates."
        ],
        solution: [
          "<spam class='fw-bold'>Automated Smart Scheduling:</spam> AI-driven scheduling engine aligns candidate availability with hiring manager calendars in real time.",
          "<spam class='fw-bold'>Virtual & In-person Options:</spam> Seamless integration with meeting tools like Zoom, Google Meet, or Microsoft Teams for virtual interviews, and smart slot allocation for physical interviews.",
          "<spam class='fw-bold'>Dynamic Rescheduling:</spam> Automated notifications for rescheduling or cancellations to ensure smooth coordination.",
          "<spam class='fw-bold'>Centralized Dashboard:</spam> Recruiters and hiring managers track all interview statuses and updates from a single interface."
        ],
        impact: [
          { text: "<span class='highlight-pink'>70% reduction</span> in time spent coordinating interviews." },
          { text: "<span class='highlight-pink'>Higher candidate turnout</span> with clear communication and instant confirmations." },
          { text: "<span class='highlight-pink'>Improved recruiter productivity,</span> freeing up bandwidth for strategic tasks." },
          { text: "<span class='highlight-pink'>Enhanced candidate experience,</span> projecting the organization as professional and tech-savvy." }
        ]
      },
      {
        id: "post-offer",
        title: "Post Offer Follow-up",
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none"><path d="M16.9591 26.1417C17.0034 26.2521 17.0805 26.3464 17.1799 26.4118C17.2794 26.4772 17.3964 26.5106 17.5154 26.5076C17.6344 26.5045 17.7496 26.4652 17.8456 26.3947C17.9415 26.3243 18.0136 26.2263 18.0523 26.1137L25.6356 3.947C25.6729 3.84363 25.6801 3.73176 25.6561 3.62448C25.6322 3.5172 25.5782 3.41896 25.5005 3.34124C25.4228 3.26352 25.3246 3.20954 25.2173 3.18562C25.11 3.1617 24.9981 3.16883 24.8948 3.20617L2.72809 10.7895C2.6155 10.8281 2.51742 10.9002 2.44701 10.9962C2.3766 11.0922 2.33722 11.2074 2.33418 11.3264C2.33113 11.4453 2.36455 11.5624 2.42996 11.6618C2.49537 11.7613 2.58963 11.8383 2.70009 11.8827L11.9518 15.5927C12.2442 15.7098 12.51 15.8849 12.7329 16.1074C12.9559 16.33 13.1315 16.5954 13.2491 16.8877L16.9591 26.1417Z" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M25.4977 3.34558L12.7334 16.1087" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        scenario: "Once an offer is released to the candidate, reach-out to them whether they have any queries on the offer and to nudge them to accept the offer at the earliest to move to the final step of hiring.",
        challenge: [
          "<b>Delayed communication</b> often leads to uncertainty and candidate drop-offs.",
          "Recruiters spend <b>excessive manual effort</b> following up individually with multiple candidates.",
          "Lack of <b>structured communication</b> fails to address key candidate concerns proactively.",
          "Competitive offers can lead to candidates <b>ghosting or renegotiating</b> at the last minute."
        ],
        solution: [
          "<spam class='fw-bold'>Automated Voice Follow-ups:</spam> AI-driven calls and messages initiate personalized conversations to check on candidate queries and intent.",
          "<spam class='fw-bold'>Real-time Query Capture:</spam> Collects and routes candidate questions directly to recruiters for prompt resolution.",
          "<spam class='fw-bold'>Consistent Engagement:</spam> Scheduled reminders keep candidates engaged until acceptance and joining.",
          "<spam class='fw-bold'>Sentiment Analysis:</spam> AI evaluates candidate tone and responses to flag potential risks of drop-offs early.",
          "<spam class='fw-bold'>Instant Insights Dashboard:</spam> Recruiters receive real-time updates on candidate responses, concerns, and likelihood of acceptance."
        ],
        impact: [
          { text: "<span class='highlight-pink'>30% reduction</span> in post-offer drop-offs." },
          { text: "<span class='highlight-pink'>Faster acceptance timelines,</span> reducing average time-to-join by up to 40%." },
          { text: "Improved candidate satisfaction through <span class='highlight-pink'>timely, transparent, and professional communication.</span>" },
          { text: "Increased recruiter efficiency, freeing time for strategic hiring tasks." }
        ]
      },
      {
        id: "pre-onboarding",
        title: "Pre-Onboarding",
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none"><path d="M18.6676 24.6216V22.2881C18.6676 21.0503 18.1759 19.8633 17.3006 18.988C16.4254 18.1128 15.2383 17.6211 14.0005 17.6211H7.00002C5.76225 17.6211 4.57518 18.1128 3.69994 18.988C2.82471 19.8633 2.33301 21.0503 2.33301 22.2881V24.6216" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M10.5 12.9551C13.0775 12.9551 15.167 10.8656 15.167 8.28811C15.167 5.71059 13.0775 3.62109 10.5 3.62109C7.9225 3.62109 5.83301 5.71059 5.83301 8.28811C5.83301 10.8656 7.9225 12.9551 10.5 12.9551Z" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M22.166 9.45398V16.4545" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M25.6665 12.954H18.666" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        scenario: "Once the offer is accepted, companies need to start pre-onboarding processes like collecting documents for Back-Ground Verification process. This stage involves following-up up for submission of documents as per the list and even for missing/correct documents.",
        challenge: ["Manual follow-ups are <>time-intensive</b> and prone to errors.",
          "Candidates often <b>delay or forget</b> document submissions, causing onboarding delays.",
          "Lack of clarity or incomplete instructions leads to <b>incorrect or missing documents.</b>",
          "Recruiters and HR teams <b>struggle with tracking</b> submissions and sending reminders efficiently."
        ],

        solution: [
          "<spam class='fw-bold'>Automated Document Collection Follow-ups:</spam> AI-powered calls and reminders prompt candidates to submit required documents promptly.",
          "<spam class='fw-bold'>Personalized Checklists:</spam> Candidates receive clear, role-specific documentation lists with step-by-step instructions.",
          "<spam class='fw-bold'>Smart Reminders for Missing/Incorrect Docs:</spam> Automated nudges help candidates correct and resubmit documents quickly.",
          "<spam class='fw-bold'>Seamless Integration:</spam> Syncs with HRMS or onboarding systems for real-time tracking of submission status.",
          "<spam class='fw-bold'>Centralized Dashboard:</spam> Recruiters and HR teams get a unified view of progress, enabling proactive escalation where needed."
        ],
        impact: [
          { text: "<span class='highlight-pink'>50% faster completion</span> of pre-onboarding documentation." },
          { text: "<span class='highlight-pink'>Significant reduction</span> in manual follow-up efforts for recruiters and HR teams." },
          { text: "Enhanced <span class='highlight-pink'>compliance and accuracy</span> in BGV processes." },
          { text: "Improved candidate experience with <span class='highlight-pink'>clear, consistent, and professional communication</span> before joining." }
        ]
      },
      {
        id: "notice-period",
        title: "Notice Period Engagement",
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="29" viewBox="0 0 28 29" fill="none"><path d="M18.666 8.56812H25.6665V15.5686" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/><path d="M25.6673 8.56812L15.7507 18.4848L9.91732 12.6514L2.33398 20.2348" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/></svg>',
        scenario: "Once the offer is accepted and firm date of joining has been confirmed, engaging with the candidates during notice period to ensure they join as per the agreed date and to make them familiar with the organization via key updates at regular frequencies.",
        challenge: [
          "Drop-offs during notice periods due to competing offers or counteroffers.",
          "Limited and inconsistent engagement, making candidates feel disconnected from the organization.",
          "Lack of structured communication to share updates, address queries, or reinforce the employer brand.",
          "Manual follow-ups by recruiters lead to time inefficiencies and delayed responses."
        ],
        solution: [
          "<spam class='fw-bold'>Automated Engagement Touchpoints:</spam> AI-powered calls, emails, and WhatsApp nudges keep candidates engaged with regular, personalized updates.",
          "<spam class='fw-bold'>Personalized Content Delivery:</spam> Share company news, leadership messages, role-specific insights, and onboarding preparation tips.",
          "<spam class='fw-bold'>Sentiment & Intent Tracking:</spam> AI analyzes responses to detect disengagement risks early, enabling proactive intervention.",
          "<spam class='fw-bold'>Interactive Q&A:</spam> Candidates can raise queries during automated interactions, ensuring concerns are addressed promptly.",
          "<spam class='fw-bold'>Integrated Dashboard:</spam> Recruiters get a real-time view of candidate engagement levels and potential risk flags."
        ],
        impact: [
          { text: "<span class='highlight-pink'>Up to 35% reduction</span> in notice period drop-offs." },
          { text: "Stronger <span class='highlight-pink'>candidate-employer connection,</span> ensuring smoother Day 1 onboarding." },
          { text: "Improved recruiter efficiency by <span class='highlight-pink'>automating routine follow-ups.</span>" },
          { text: "Positive candidate experience that reflects a <span class='highlight-pink'>professional and welcoming culture.</span>" }
        ]
      }
    ];

    const workflowTabs = document.getElementById("workflowTabs");
    const sectionContainer = document.getElementById("sectionContainer");

    // Render Tabs
    sections.forEach((sec, i) => {
      const tab = document.createElement("div");
      tab.className = "workflow-tab" + (i === 0 ? " active" : "");
      tab.dataset.id = sec.id;
      tab.innerHTML = `${sec.icon} ${sec.title}`;
      workflowTabs.appendChild(tab);

      if (i < sections.length - 1) {
        const arrow = document.createElement("div");
        arrow.className = "tab-arrow";
        arrow.innerHTML = `<img src="./assets/images/arrow.png" alt="arrow" />`;
        workflowTabs.appendChild(arrow);
      }
    });

    function renderSection(id) {
      const sec = sections.find(s => s.id === id);
      sectionContainer.innerHTML = `
        <div class="section-card">
          <div class="row align-items-center section-left-card p-3 "> 
            <h3 class="section-title">
              <div class="talent-icon">
                ${sec.icon}
              </div>
              ${sec.title}
            </h3>


            <div class="scenario">
              <strong> <img src="./assets/images/orangeWarning.png" alt="Warning" /> Scenario</strong>
              ${sec.scenario}
            </div>

           <div class="challenge">
            <strong>
              <img src="./assets/images/blueWarning.png" alt="Warning" /> Challenge
            </strong>
            <ul>
              ${sec.challenge.map(ch => `<li>${ch}</li>`).join("")}
            </ul>
           </div>


            <div class="solution">
            <strong>
              <img src="./assets/images/purpleWarning.png" alt="Warning" /> Solution with Callify
            </strong>
            <ul>
              ${sec.solution.map(sol => `<li>${sol}</li>`).join("")}
            </ul>
           </div>
          </div>


          <div class="d-flex flex-column align-items-start impact-section">
            <h5 class="mt-2">Impact</h5>
            <div class="impact-grid">
              ${sec.impact.map(item => `
                <div class="col-md-6">
                  <div class="impact-box">
                    ${item.text}
                  </div>
                </div>
              `).join("")}
            </div>
          </div>
        </div>
      `;
    }

    // Default load first section
    renderSection(sections[0].id);

    // Tab click functionality
    workflowTabs.addEventListener("click", (e) => {
      if (e.target.closest(".workflow-tab")) {
        document.querySelectorAll(".workflow-tab").forEach(tab => tab.classList.remove("active"));
        e.target.closest(".workflow-tab").classList.add("active");
        renderSection(e.target.closest(".workflow-tab").dataset.id);
      }
    });
  </script>

  <script>
    const buttons = document.querySelectorAll(".toggle-btn");

    buttons.forEach((btn) => {
      btn.addEventListener("click", () => {
        buttons.forEach((b) => b.classList.remove("active"));
        btn.classList.add("active");
      });
    });
  </script>
</body>

</html>